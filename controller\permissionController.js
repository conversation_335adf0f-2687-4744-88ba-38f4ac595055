const permissionService = require('../services/permissionService');
const db = require('../config/database');

class PermissionController {
  /**
   * <PERSON><PERSON>n thị danh sách permissions
   */
  async index(req, res) {
    try {
      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Permissions', url:'/admin/permissions', active: true},
      ];
      
      res.render('admin/permissions', {
        title: 'Quản lý Permissions',
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in permissions index:', error);
      res.status(500).render('500', { error: error.message });
    }
  }

  /**
   * Hiển thị form tạo permission mới
   */
  async create(req, res) {
    try {
      // Lấy danh sách bảng hiện có
      const tables = await db.query(`
        SELECT name, display_name FROM admintable WHERE is_active = 1 ORDER BY display_name
      `);

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Permissions', url:'/admin/permissions', active: false},
        {name: 'Create', url:'/admin/permissions/create', active: true},
      ];
      res.render('admin/permission-form', {
        title: 'Tạo Permission Mới',
        permission: null,
        tables,
        actions: ['read', 'edit', 'add', 'delete'],
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in permissions create:', error);
      res.status(500).render('500', { error: error.message });
    }
  }

  /**
   * Lưu permission mới
   */
  async store(req, res) {
    try {
      const { name, display_name, description, table_name, action } = req.body;

      // Validate dữ liệu
      if (!name || !display_name || !action) {
        return res.status(400).json({
          success: false,
          message: 'Tên, tên hiển thị và hành động là bắt buộc'
        });
      }

      // Kiểm tra tên permission đã tồn tại chưa
      const existingPermission = await db.queryOne(`
        SELECT id FROM permissions WHERE name = ?
      `, [name]);

      if (existingPermission) {
        return res.status(400).json({
          success: false,
          message: 'Tên permission đã tồn tại'
        });
      }

      // Tạo permission
      const permissionId = await permissionService.createPermission({
        name,
        display_name,
        description,
        table_name,
        action
      });

      res.json({
        success: true,
        message: 'Permission đã được tạo thành công',
        data: { id: permissionId }
      });
    } catch (error) {
      console.error('Error in permissions store:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi tạo permission: ' + error.message
      });
    }
  }

  /**
   * Hiển thị chi tiết permission
   */
  async show(req, res) {
    try {
      const { id } = req.params;

      const permission = await db.queryOne(`
        SELECT * FROM permissions WHERE id = ?
      `, [id]);

      if (!permission) {
        return res.status(404).render('404', { message: 'Permission không tồn tại' });
      }

      // Lấy danh sách roles có permission này
      const roles = await db.query(`
        SELECT r.id, r.name, rp.granted_at
        FROM role_permissions rp
        INNER JOIN role r ON rp.role_id = r.id
        WHERE rp.permission_id = ?
        ORDER BY r.name
      `, [id]);

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Permissions', url:'/admin/permissions', active: false},
        {name: 'Detail', url:'/admin/permissions', active: true},
      ];

      res.render('admin/permission-detail', {
        title: `Permission: ${permission.display_name}`,
        permission,
        roles,
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in permissions show:', error);
      res.status(500).render('500', { error: error.message });
    }
  }

  /**
   * Hiển thị form chỉnh sửa permission
   */
  async edit(req, res) {
    try {
      const { id } = req.params;

      const permission = await db.queryOne(`
        SELECT * FROM permissions WHERE id = ?
      `, [id]);

      if (!permission) {
        return res.status(404).render('404', { message: 'Permission không tồn tại' });
      }

      // Lấy danh sách bảng hiện có
      const tables = await db.query(`
        SELECT name, display_name FROM admintable WHERE is_active = 1 ORDER BY display_name
      `);

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Permissions', url:'/admin/permissions', active: false},
        {name: 'Edit', url:'/admin/permissions/edit', active: true},
      ];

      res.render('admin/permission-form', {
        title: 'Chỉnh sửa Permission',
        permission,
        tables,
        actions: ['read', 'edit', 'add', 'delete'],
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in permissions edit:', error);
      res.status(500).render('500', { error: error.message });
    }
  }

  /**
   * Cập nhật permission
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const { display_name, description } = req.body;

      // Validate dữ liệu
      if (!display_name) {
        return res.status(400).json({
          success: false,
          message: 'Tên hiển thị là bắt buộc'
        });
      }

      // Kiểm tra permission có tồn tại không
      const permission = await db.queryOne(`
        SELECT id FROM permissions WHERE id = ?
      `, [id]);

      if (!permission) {
        return res.status(404).json({
          success: false,
          message: 'Permission không tồn tại'
        });
      }

      // Cập nhật permission
      await permissionService.updatePermission(id, {
        display_name,
        description
      });

      res.json({
        success: true,
        message: 'Permission đã được cập nhật thành công'
      });
    } catch (error) {
      console.error('Error in permissions update:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi cập nhật permission: ' + error.message
      });
    }
  }

  /**
   * Xóa permission
   */
  async destroy(req, res) {
    try {
      const { id } = req.params;

      // Kiểm tra permission có tồn tại không
      const permission = await db.queryOne(`
        SELECT id, name FROM permissions WHERE id = ?
      `, [id]);

      if (!permission) {
        return res.status(404).json({
          success: false,
          message: 'Permission không tồn tại'
        });
      }

      // Kiểm tra có role nào đang sử dụng permission này không
      const roleCount = await db.queryOne(`
        SELECT COUNT(*) as count FROM role_permissions WHERE permission_id = ?
      `, [id]);

      if (roleCount.count > 0) {
        return res.status(400).json({
          success: false,
          message: `Không thể xóa permission này vì có ${roleCount.count} role đang sử dụng`
        });
      }

      // Xóa permission
      await permissionService.deletePermission(id);

      res.json({
        success: true,
        message: 'Permission đã được xóa thành công'
      });
    } catch (error) {
      console.error('Error in permissions destroy:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi xóa permission: ' + error.message
      });
    }
  }

  /**
   * Đồng bộ permissions cho tất cả bảng
   */
  async sync(req, res) {
    try {
      const result = await permissionService.syncAllTablePermissions();

      res.json({
        success: true,
        message: `Đồng bộ thành công: ${result.permissionsCreated} permissions cho ${result.tablesProcessed} bảng`,
        data: result
      });
    } catch (error) {
      console.error('Error in permissions sync:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi đồng bộ permissions: ' + error.message
      });
    }
  }

  /**
   * Lấy permissions của bảng cụ thể (API)
   */
  async getTablePermissions(req, res) {
    try {
      const { tableName } = req.params;

      const permissions = await permissionService.getTablePermissions(tableName);

      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      console.error('Error getting table permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi lấy permissions: ' + error.message
      });
    }
  }

  /**
   * Tạo permissions cho bảng mới (API)
   */
  async createTablePermissions(req, res) {
    try {
      const { tableName, displayName } = req.body;

      if (!tableName || !displayName) {
        return res.status(400).json({
          success: false,
          message: 'Tên bảng và tên hiển thị là bắt buộc'
        });
      }

      const permissions = await permissionService.createTablePermissions(tableName, displayName);

      res.json({
        success: true,
        message: `Đã tạo ${permissions.length} permissions cho bảng ${tableName}`,
        data: permissions
      });
    } catch (error) {
      console.error('Error creating table permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi tạo permissions cho bảng: ' + error.message
      });
    }
  }
  /**
   * API: DataTables endpoint for permissions
   */
  async api(req, res) {
    try {
      // DataTables parameters
      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';
      
      // Custom filters
      const search = req.query.search_custom || searchValue;
      const table_name = req.query.table_name || '';
      const action = req.query.action || '';

      // Order
      const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
      const orderDirection = req.query.order?.[0]?.dir || 'desc';
      
      // Column mapping for ordering
      const columns = ['id', 'name', 'display_name', 'table_name', 'action', 'description'];
      const orderBy = columns[orderColumnIndex] || 'id';

      // Build WHERE conditions
      let whereConditions = [];
      let queryParams = [];

      if (search) {
        whereConditions.push('(name LIKE ? OR display_name LIKE ? OR description LIKE ?)');
        queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }

      if (table_name) {
        whereConditions.push('table_name = ?');
        queryParams.push(table_name);
      }

      if (action) {
        whereConditions.push('action = ?');
        queryParams.push(action);
      }

      const whereClause = whereConditions.length > 0 ? ` WHERE ${whereConditions.join(' AND ')}` : '';
      const offset = start;

      // Get data
      const [permissions, countResult] = await Promise.all([
        db.query(`
          SELECT id, name, display_name, description, table_name, action, created_at, updated_at
          FROM permissions
          ${whereClause}
          ORDER BY ${orderBy} ${orderDirection}
          LIMIT ${length} OFFSET ${offset}
        `, queryParams),
        db.queryOne(`SELECT COUNT(*) as total FROM permissions ${whereClause}`, queryParams)
      ]);

      // Format for DataTables
      const response = {
        draw: draw,
        recordsTotal: countResult.total,
        recordsFiltered: countResult.total,
        data: permissions
      };

      res.json(response);
    } catch (error) {
      console.error('Error in permissions API:', error);
      res.status(500).json({ 
        error: true, 
        message: error.message,
        draw: parseInt(req.query.draw) || 1,
        recordsTotal: 0,
        recordsFiltered: 0,
        data: []
      });
    }
  }

  /**
   * API: Get filter options
   */
  async filterOptions(req, res) {
    try {
      const [tables, actions] = await Promise.all([
        db.query('SELECT DISTINCT table_name FROM permissions WHERE table_name IS NOT NULL ORDER BY table_name'),
        db.query('SELECT DISTINCT action FROM permissions WHERE action IS NOT NULL ORDER BY action')
      ]);

      res.json({
        success: true,
        tables,
        actions
      });
    } catch (error) {
      console.error('Error getting filter options:', error);
      res.status(500).json({ 
        success: false, 
        message: error.message 
      });
    }
  }
}

module.exports = new PermissionController(); 