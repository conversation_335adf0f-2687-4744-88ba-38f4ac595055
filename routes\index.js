var express = require('express');
var router = express.Router();
var indexController = require('../controller/indexController'); 
const commonService = require('../services/commonService');
var device        = require('../controller/deviceController');

/* GET home page. */
router.get('/', commonService.isAuthenticated, indexController.index);

// Device Management
router.get("/devices", commonService.isAuthenticated, device.getActiveDevices);
router.get("/devices-page", commonService.isAuthenticated, (req, res) => {
    const breadcrumb = [
          {name: 'Home', url:'/', active: false},
          {name: 'Devi<PERSON>', url:'/', active: true},
      ];
    res.render('devices/devices', { user: req.user, breadcrumb: breadcrumb, errors: [], title: 'Quản lý thiết bị' });
});
router.post("/devices/logout", commonService.isAuthenticatedPost, device.logoutDevice);
router.post("/devices/logout-all-others", commonService.isAuthenticatedPost, device.logoutAllOtherDevices);
router.get("/devices/settings", commonService.isAuthenticated, device.getSessionSettings);
router.post("/devices/settings", commonService.isAuthenticatedPost, device.updateSessionSettings);

module.exports = router;
