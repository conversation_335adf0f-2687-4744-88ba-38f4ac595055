const db = require('../config/database');
const fs = require('fs');
const path = require('path');

class FileService {
  static async logUpload(userId, fileData) {
    const query = `
      INSERT INTO upload_logs (user_id, filename, original_name, file_size, mimetype, upload_path, created_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `;
    
    const result = await db.query(query, [
      userId,
      fileData.filename,
      fileData.originalname,
      fileData.size,
      fileData.mimetype,
      fileData.path
    ]);
    
    return result.insertId;
  }

  static async getFilesByUser(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const files = await db.query(`
      SELECT id, filename, original_name, file_size, mimetype, upload_path, created_at
      FROM upload_logs 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `, [userId, limit, offset]);

    const total = await db.queryOne(`
      SELECT COUNT(*) as count FROM upload_logs WHERE user_id = ?
    `, [userId]);

    return {
      files,
      pagination: {
        page,
        limit,
        total: total.count,
        totalPages: Math.ceil(total.count / limit)
      }
    };
  }

  static async deleteFile(fileId, userId) {
    const file = await db.queryOne(`
      SELECT * FROM upload_logs WHERE id = ? AND user_id = ?
    `, [fileId, userId]);

    if (!file) {
      throw new Error('File not found or access denied');
    }

    // Delete physical file
    if (fs.existsSync(file.upload_path)) {
      fs.unlinkSync(file.upload_path);
    }

    // Delete from database
    await db.query(`DELETE FROM upload_logs WHERE id = ?`, [fileId]);
    
    return file;
  }
}

module.exports = FileService;