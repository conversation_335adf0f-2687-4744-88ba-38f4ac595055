<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h3 class="card-title">File Management</h3>
          <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="fas fa-upload"></i> Upload Files
          </button>
        </div>
        
        <div class="card-body">
          <% if (files.length === 0) { %>
            <div class="text-center py-4">
              <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
              <p class="text-muted">No files uploaded yet</p>
            </div>
          <% } else { %>
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>File Name</th>
                    <th>Type</th>
                    <th>Size</th>
                    <th>Upload Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <% files.forEach(file => { %>
                    <tr>
                      <td>
                        <i class="fas fa-file me-2"></i>
                        <%= file.original_name %>
                      </td>
                      <td><span class="badge bg-info"><%= file.mimetype %></span></td>
                      <td><%= (file.file_size / 1024 / 1024).toFixed(2) %> MB</td>
                      <td><%= new Date(file.created_at).toLocaleDateString() %></td>
                      <td>
                        <a href="<%= baseUrl %>/uploads/<%= file.filename %>" 
                           class="btn btn-sm btn-outline-primary" target="_blank">
                          <i class="fas fa-eye"></i>
                        </a>
                        <button class="btn btn-sm btn-outline-danger ms-1" 
                                onclick="deleteFile(<%= file.id %>)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <% if (pagination.totalPages > 1) { %>
              <nav>
                <ul class="pagination justify-content-center">
                  <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                    <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                      <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                    </li>
                  <% } %>
                </ul>
              </nav>
            <% } %>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Upload Files</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="uploadForm" enctype="multipart/form-data">
          <div class="mb-3">
            <label for="files" class="form-label">Select Files</label>
            <input type="file" class="form-control" id="files" name="files" multiple>
            <div class="form-text">Max 10 files, 500MB total</div>
          </div>
          <div id="uploadProgress" class="d-none">
            <div class="progress">
              <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" onclick="uploadFiles()">Upload</button>
      </div>
    </div>
  </div>
</div>

<script>
async function uploadFiles() {
  const form = document.getElementById('uploadForm');
  const formData = new FormData(form);
  const progressDiv = document.getElementById('uploadProgress');
  const progressBar = progressDiv.querySelector('.progress-bar');
  
  try {
    progressDiv.classList.remove('d-none');
    progressBar.style.width = '0%';
    
    const response = await fetch('/files/upload', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.success) {
      showAlert('success', result.message);
      setTimeout(() => location.reload(), 1000);
    } else {
      showAlert('danger', result.message);
    }
  } catch (error) {
    showAlert('danger', 'Upload failed');
  } finally {
    progressDiv.classList.add('d-none');
    bootstrap.Modal.getInstance(document.getElementById('uploadModal')).hide();
  }
}

async function deleteFile(fileId) {
  if (!confirm('Are you sure you want to delete this file?')) return;
  
  try {
    const response = await fetch(`/files/${fileId}`, {
      method: 'DELETE'
    });
    
    const result = await response.json();
    
    if (result.success) {
      showAlert('success', result.message);
      setTimeout(() => location.reload(), 1000);
    } else {
      showAlert('danger', result.message);
    }
  } catch (error) {
    showAlert('danger', 'Delete failed');
  }
}

function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.querySelector('.container-fluid').prepend(alertDiv);
}
</script>