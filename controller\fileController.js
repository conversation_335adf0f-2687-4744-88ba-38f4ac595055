const FileService = require('../services/fileService');
const { Validator, ValidationError } = require('../utils/validation');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const UPLOAD_DIR = path.join(__dirname, '../uploads');
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

// Ensure upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

const FILE_CONFIGS = {
  'image/jpeg': { maxSize: 10 * 1024 * 1024, extensions: ['.jpg', '.jpeg'] },
  'image/png': { maxSize: 10 * 1024 * 1024, extensions: ['.png'] },
  'image/webp': { maxSize: 10 * 1024 * 1024, extensions: ['.webp'] },
  'image/gif': { maxSize: 10 * 1024 * 1024, extensions: ['.gif'] },
  'application/pdf': { maxSize: 50 * 1024 * 1024, extensions: ['.pdf'] },
  'video/mp4': { maxSize: 100 * 1024 * 1024, extensions: ['.mp4'] },
  'application/zip': { maxSize: 100 * 1024 * 1024, extensions: ['.zip'] },
  'text/plain': { maxSize: 5 * 1024 * 1024, extensions: ['.txt'] }
};

const validateFileType = (file) => {
  const config = FILE_CONFIGS[file.mimetype];
  if (!config) {
    return { valid: false, error: `File type not allowed: ${file.mimetype}` };
  }

  const fileExtension = path.extname(file.originalname).toLowerCase();
  if (!config.extensions.includes(fileExtension)) {
    return { 
      valid: false, 
      error: `File extension ${fileExtension} not allowed for ${file.mimetype}` 
    };
  }

  return { valid: true };
};

const validateFileSize = (file) => {
  const config = FILE_CONFIGS[file.mimetype];
  if (file.size > config.maxSize) {
    return { 
      valid: false, 
      error: `File size exceeds limit of ${(config.maxSize / 1024 / 1024).toFixed(2)}MB` 
    };
  }
  return { valid: true };
};

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, UPLOAD_DIR);
  },
  filename: (req, file, cb) => {
    const safeOriginalName = path.basename(file.originalname).replace(/[^a-zA-Z0-9._-]/g, '');
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + '-' + safeOriginalName);
  }
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    const typeValidation = validateFileType(file);
    if (!typeValidation.valid) {
      return cb(new Error(typeValidation.error), false);
    }
    cb(null, true);
  },
  limits: {
    fileSize: 500 * 1024 * 1024,
    files: 10
  }
});

class FileController {
  static uploadFiles = upload.array('files', 10);

  static async upload(req, res) {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ 
          success: false, 
          message: 'No files were uploaded.' 
        });
      }

      const uploadedFiles = [];
      const errors = [];

      for (const file of req.files) {
        const sizeValidation = validateFileSize(file);
        
        if (!sizeValidation.valid) {
          fs.unlinkSync(file.path);
          errors.push({
            filename: file.originalname,
            error: sizeValidation.error
          });
          continue;
        }

        try {
          await FileService.logUpload(req.user.id, file);
          
          uploadedFiles.push({
            url: `${BASE_URL}/uploads/${file.filename}`,
            filename: file.filename,
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            sizeFormatted: `${(file.size / 1024 / 1024).toFixed(2)} MB`
          });
        } catch (dbError) {
          console.error('Database logging error:', dbError);
          fs.unlinkSync(file.path);
          errors.push({
            filename: file.originalname,
            error: 'Database error'
          });
        }
      }

      const response = {
        success: uploadedFiles.length > 0,
        message: `${uploadedFiles.length} files uploaded successfully.`,
        files: uploadedFiles
      };

      if (errors.length > 0) {
        response.errors = errors;
        response.message += ` ${errors.length} files failed.`;
      }

      res.status(uploadedFiles.length > 0 ? 200 : 400).json(response);
    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({
        success: false,
        message: 'Upload failed'
      });
    }
  }

  static async index(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      
      const result = await FileService.getFilesByUser(req.user.id, page, limit);
      
      res.render('files/index', {
        title: 'File Management',
        files: result.files,
        pagination: result.pagination,
        baseUrl: BASE_URL
      });
    } catch (error) {
      console.error('Error loading files:', error);
      res.status(500).render('error', { message: 'Error loading files' });
    }
  }

  static async delete(req, res) {
    try {
      const fileId = parseInt(req.params.id);
      
      if (!fileId) {
        return res.status(400).json({
          success: false,
          message: 'Invalid file ID'
        });
      }

      await FileService.deleteFile(fileId, req.user.id);
      
      res.json({
        success: true,
        message: 'File deleted successfully'
      });
    } catch (error) {
      console.error('Delete error:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Delete failed'
      });
    }
  }
}

module.exports = FileController;