const passport = require('passport');
const userService = require('../services/userService');
const commonService = require('../services/commonService');
const jwtService = require('../services/jwtService');
const bcrypt = require('bcrypt');

module.exports = {
  register: async (req, res) => {
    try {
      const { fullname, email, password, confirm_password } = req.body;

      // Validate input
      if (!fullname || !email || !password || !confirm_password) {
        return res.status(400).json({ message: 'Please fill in all fields.' });
      }

      if (password !== confirm_password) {
        return res.status(400).json({ message: 'Passwords do not match.' });
      }

      // Check if email format is valid
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: 'Invalid email format.' } });
      }

      // Check if email already exists
      const existingUser = await userService.findUserByEmail(email);

      if (existingUser) {
        return res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: 'Email already exists.' } });
      }

      // Create user in database
      const user = await userService.createUser(fullname, email, password);

      // Redirect to login page
      res.redirect('/user/login');
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred during registration.' });
    }
  },
  /**
   * Handles user login with reCAPTCHA verification and JWT token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Object} JSON response with login result
   */
  login: async function (req, res, next) {
     try {
          // Step 2: Authenticate user manually (thay vì dùng passport)
          const { email, password } = req.body;
          
          if (!email || !password) {
              return res.render('login', {
                title: 'Login',
                layout: 'login_layout',
                messages: { error: 'Vui lòng nhập đầy đủ email và mật khẩu' }
              });
          }

          try {
              // Tìm user trong database
              const userResult = await commonService.getAllDataTable('user', { email: email });
              
              if (!userResult.success || !userResult.data || userResult.data.length === 0) {
                  return res.render('login', {
                    title: 'Login',
                    layout: 'login_layout',
                    messages: { error: 'Tài khoản không tồn tại' }
                  });
              }

              const user = userResult.data[0];
              
              // Kiểm tra mật khẩu
              const isValidPassword = await bcrypt.compare(password, user.password);
              if (!isValidPassword) {
                  return res.render('login', {
                    title: 'Login',
                    layout: 'login_layout',
                    messages: { error: 'Sai mật khẩu' }
                  });
              }

              // Kiểm tra tài khoản có active không
              if (user.active !== 1) {
                   return res.render('login', {
                    title: 'Login',
                    layout: 'login_layout',
                    messages: { error: 'Tài khoản chưa được kích hoạt' }
                  });
              }

              // Tạo JWT token với token ID duy nhất
              const { token: jwtToken, tokenId } = jwtService.createToken(user);

              // Lấy thông tin thiết bị
              const deviceInfo = jwtService.getDeviceInfo(req);

              // Lưu token vào database với multi-device support
              const saveResult = await jwtService.saveTokenToDatabase(user.id, tokenId, deviceInfo, req.ip);

              if (!saveResult.success) {
                  return res.render('login', {
                    title: 'Login',
                    layout: 'login_layout',
                    messages: { error: saveResult.message }
                  });
              }

              // Set JWT cookie
              req.app.locals.setJWTCookie(res, jwtToken);
              // Redirect to home page
              return res.redirect('/');
          } catch (authError) {
              console.error('Authentication error:', authError);
              return res.render('login', {
                title: 'Login',
                layout: 'login_layout',
                messages: { error: 'Lỗi xác thực, vui lòng thử lại sau' }
              });
          }

      } catch (globalError) {
          console.error("Unexpected login error:", globalError);
          return res.render('login', {
            title: 'Login',
            layout: 'login_layout',
            messages: { error: "Đã xảy ra lỗi không mong muốn" }
          });
      }
  },
  logout: async function (req, res, next) {
      if (req.user && req.user.tokenId) {
          try {
              // Xóa token khỏi database
              await jwtService.removeTokenFromDatabase(req.user.id, req.user.tokenId);

              // Xóa cache user nếu có
              const cacheService = require('../services/cacheService');
              cacheService.invalidateUser(req.user.id);

          } catch (error) {
              console.error('Logout error:', error);
          }
      }

      // Xóa JWT cookie
      req.app.locals.clearJWTCookie(res);
      
      res.redirect('/user/login');
  },
  getRegister: function (req, res, next) {
    console.log('getRegister');
    try {
      if (req.user) {
        res.redirect('/');
      }else{
        res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: '' } });
      }
    } catch (error) {
      console.error(error);
    }
  },
  getLogin: function (req, res, next) {
    console.log('getLogin', req.user);
    try {
      if (req.user) {
        res.redirect('/');
      }else{
        res.render('login', { title: 'Login', layout: 'login_layout', messages: { error: '' } });
      }
    } catch (error) {
      console.error(error);
    }
  },
  checkEmail: async (req, res) => {
    try {
      const { email } = req.body;

      const existingUser = await userService.findUserByEmail(email);

      res.json({ exists: !!existingUser });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred during email check.' });
    }
  },
  index: async function (req, res, next) {
    const breadcrumb = [
          {name: 'Home', url:'/', active: false},
          {name: 'User', url:'/', active: true},
      ];
      res.render('user/index', { title: 'Quản lý user', breadcrumb: breadcrumb });
  },
  getData: async function (req, res, next) {
    try {
      // Get DataTables parameters
      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';

      // Calculate page number
      const page = Math.floor(start / length) + 1;

      // Get paginated data
      const result = await userService.getUsersWithPagination(page, length, searchValue);

      // Format data for DataTables
      const formattedUsers = result.data.map(user => ({
        id: user.id,
        fullname: user.fullname,
        email: user.email,
        createdAt: user.createdAt ? new Date(user.createdAt).toLocaleDateString() : '',
        updatedAt: user.updatedAt ? new Date(user.updatedAt).toLocaleDateString() : ''
      }));
      console.log('result', result);
      // Return DataTables server-side format
      res.json({
        draw: draw,
        recordsTotal: result.total,
        recordsFiltered: result.totalFiltered,
        data: formattedUsers
      });
    } catch (error) {
      console.error('Error fetching user data:', error);
      res.status(500).json({ error: 'Failed to fetch user data' });
    }
  },
  addUser: async (req, res) => {
    try {
      const { fullname, email, password } = req.body;
      const user = await userService.addUser(fullname, email, password);
      res.status(201).json(user);
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred while adding user.' });
    }
  },
  editUser: async (req, res) => {
    try {
      const { id } = req.params;
      const { fullname, email, password } = req.body;
      const user = await userService.editUser(id, fullname, email, password);
      res.status(200).json(user);
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred while editing user.' });
    }
  },
  deleteUser: async (req, res) => {
    try {
      const { id } = req.params;
      await userService.deleteUser(id);
      res.status(204).send();
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred while deleting user.' });
    }
  }
};
