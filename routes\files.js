const express = require('express');
const router = express.Router();
const passport = require('passport');
const FileController = require('../controller/fileController');

// Middleware xác thực
const requireAuth = passport.authenticate('jwt', { session: false });

// Upload files
router.post('/upload', requireAuth, FileController.uploadFiles, FileController.upload);

// View files page
router.get('/', requireAuth, FileController.index);

// Delete file
router.delete('/:id', requireAuth, FileController.delete);

module.exports = router;