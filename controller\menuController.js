const menuService = require('../services/menuService');
const adminService = require('../services/adminService');

const menuController = {
  // Trang quản lý menu
  index: async (req, res) => {
    try {
      // Lấy permissions của user cho table admin_menus
      const permissionService = require('../services/permissionService');
      const userPermissions = {
        canBrowse: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'browse'),
        canRead: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'read'),
        canCreate: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'create'),
        canUpdate: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'update'),
        canDelete: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'delete')
      };

      // Nếu không có quyền browse thì redirect về trang chủ với thông báo
      if (!userPermissions.canBrowse) {
        req.flash('error', 'Bạn không có quyền xem danh sách Admin Menus');
        return res.redirect('/');
      }

      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: false },
        { name: 'Menu Management', url: '/admin/menus', active: true }
      ];

      res.render('admin/menus', {
        title: 'Menu Management',
        breadcrumb,
        userPermissions,
        user: req.user
      });
    } catch (error) {
      console.error('Error loading menu management page:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },

  // API lấy danh sách menu
  getMenusData: async (req, res) => {
    try {
      // Kiểm tra quyền browse cho table admin_menus
      const permissionService = require('../services/permissionService');
      const canBrowse = await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'browse');
      
      if (!canBrowse) {
        return res.status(403).json({
          success: false,
          message: 'Bạn không có quyền xem danh sách Admin Menus',
          draw: parseInt(req.query.draw) || 1,
          recordsTotal: 0,
          recordsFiltered: 0,
          data: []
        });
      }

      const menus = await menuService.getFlatMenuList();

      // Lấy permissions của user cho các actions khác
      const userPermissions = {
        canRead: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'read'),
        canUpdate: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'update'),
        canDelete: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'delete')
      };

      const formattedMenus = menus.map(menu => {
        // Tạo actions dựa trên quyền
        let actions = [];
        
        if (userPermissions.canRead) {
          actions.push(`
            <button class="btn btn-sm btn-info view-menu" data-id="${menu.id}" title="View Details">
              <i class="fas fa-eye"></i>
            </button>
          `);
        }
        
        if (userPermissions.canUpdate) {
          actions.push(`
            <button class="btn btn-sm btn-warning edit-menu" data-id="${menu.id}" title="Edit">
              <i class="fas fa-edit"></i>
            </button>
          `);
        }
        
        if (userPermissions.canDelete) {
          actions.push(`
            <button class="btn btn-sm btn-danger delete-menu" data-id="${menu.id}" data-title="${menu.title}" title="Delete">
              <i class="fas fa-trash"></i>
            </button>
          `);
        }

        return {
          id: menu.id,
          title: menu.title,
          url: menu.url || '-',
          icon: menu.icon || '-',
          parent: menu.parent ? menu.parent.title : '-',
          order_index: menu.order_index,
          is_active: menu.is_active,
          is_title: menu.is_title,
          is_divider: menu.is_divider,
          badge_text: menu.badge_text || '-',
          table_name: menu.table ? menu.table.display_name : '-',
          children_count: menus.filter(m => m.parent_id === menu.id).length,
          created_at: menu.created_at,
          actions: actions.length > 0 ? actions.join('') : '<span class="text-muted">No actions</span>'
        };
      });

      res.json({
        draw: parseInt(req.query.draw) || 1,
        recordsTotal: formattedMenus.length,
        recordsFiltered: formattedMenus.length,
        data: formattedMenus
      });
    } catch (error) {
      console.error('Error getting menus data:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Lấy menu theo ID
  getMenu: async (req, res) => {
    try {
      // Kiểm tra quyền read cho table admin_menus
      const permissionService = require('../services/permissionService');
      const canRead = await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'read');
      
      if (!canRead) {
        return res.status(403).json({ 
          success: false, 
          message: 'Bạn không có quyền xem chi tiết Admin Menus' 
        });
      }

      const { id } = req.params;
      const menu = await menuService.getMenuById(id);
      
      if (!menu) {
        return res.status(404).json({ success: false, message: 'Menu not found' });
      }

      res.json({ success: true, data: menu });
    } catch (error) {
      console.error('Error getting menu:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Tạo menu mới
  createMenu: async (req, res) => {
    try {
      const menuData = processMenuData(req.body);
      const menu = await menuService.createMenu(menuData);
      res.status(201).json({ success: true, data: menu, message: 'Menu created successfully' });
    } catch (error) {
      console.error('Error creating menu:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Cập nhật menu
  updateMenu: async (req, res) => {
    try {
      const { id } = req.params;
      const menuData = processMenuData(req.body);
      const menu = await menuService.updateMenu(id, menuData);
      res.json({ success: true, data: menu, message: 'Menu updated successfully' });
    } catch (error) {
      console.error('Error updating menu:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Xóa menu
  deleteMenu: async (req, res) => {
    try {
      const { id } = req.params;
      await menuService.deleteMenu(id);
      res.json({ success: true, message: 'Menu deleted successfully' });
    } catch (error) {
      console.error('Error deleting menu:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Đồng bộ menu từ tables
  syncTableMenus: async (req, res) => {
    try {
      const result = await menuService.syncTableMenus();
      res.json({ success: true, message: result.message });
    } catch (error) {
      console.error('Error syncing table menus:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Cập nhật thứ tự menu
  updateMenuOrder: async (req, res) => {
    try {
      const { menuOrders } = req.body;
      const result = await menuService.updateMenuOrder(menuOrders);
      res.json({ success: true, message: result.message });
    } catch (error) {
      console.error('Error updating menu order:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Tạo menu mặc định
  createDefaultMenus: async (req, res) => {
    try {
      const result = await menuService.createDefaultMenus();
      res.json({ success: true, message: result.message });
    } catch (error) {
      console.error('Error creating default menus:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API lấy menu cho sidebar
  getSidebarMenus: async (req, res) => {
    try {
      const menus = await menuService.getAllMenus();
      
      // Nếu user chưa đăng nhập, trả về menu cơ bản
      if (!req.user || !req.user.id) {
        const basicMenus = menus.filter(menu => 
          !menu.table_id && (menu.url === '/' || menu.url === '/login')
        );
        return res.json({ success: true, data: basicMenus });
      }

      // Filter menus dựa trên quyền user
      const permissionService = require('../services/permissionService');
      const filteredMenus = await filterMenusByPermissions(menus, req.user.id, permissionService);
      
      res.json({ success: true, data: filteredMenus });
    } catch (error) {
      console.error('Error getting sidebar menus:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API lấy danh sách parent menus cho dropdown
  getParentMenus: async (req, res) => {
    try {
      const menus = await menuService.getFlatMenuList();
      const parentMenus = menus.filter(menu => !menu.is_divider && !menu.table_id);
      
      res.json({ 
        success: true, 
        data: parentMenus.map(menu => ({
          id: menu.id,
          title: menu.title,
          level: menu.parent_id ? 1 : 0
        }))
      });
    } catch (error) {
      console.error('Error getting parent menus:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API lấy danh sách admin tables cho dropdown
  getAdminTables: async (req, res) => {
    try {
      const tables = await adminService.getAllAdminTables();
      
      res.json({ 
        success: true, 
        data: tables.map(table => ({
          id: table.id,
          name: table.name,
          display_name: table.display_name
        }))
      });
    } catch (error) {
      console.error('Error getting admin tables:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  }
};

// Helper function để xử lý dữ liệu menu từ form
function processMenuData(rawData) {
  const processedData = { ...rawData };

  // Convert boolean fields
  const booleanFields = ['is_active', 'is_title', 'is_divider'];
  booleanFields.forEach(field => {
    if (processedData[field] !== undefined) {
      // Convert string 'true'/'false', 'on'/undefined, hoặc boolean values
      processedData[field] = processedData[field] === true ||
                            processedData[field] === 'true' ||
                            processedData[field] === 'on';
    }
  });

  // Convert numeric fields
  const numericFields = ['parent_id', 'order_index', 'table_id'];
  numericFields.forEach(field => {
    if (processedData[field] !== undefined && processedData[field] !== '') {
      const numValue = parseInt(processedData[field]);
      processedData[field] = isNaN(numValue) ? null : numValue;
    } else {
      processedData[field] = null;
    }
  });

  // Tự động tạo URL nếu chưa có URL nhưng đã chọn table
  if ((!processedData.url || processedData.url.trim() === '') && processedData.table_id) {
    processedData.url = `/admin/tables/${processedData.table_id}/data`;
  }

  // Clean up empty string values
  Object.keys(processedData).forEach(key => {
    if (processedData[key] === '') {
      processedData[key] = null;
    }
  });

  return processedData;
}

// Helper function để filter menus dựa trên quyền user
async function filterMenusByPermissions(menus, userId, permissionService) {
  try {
    // Kiểm tra nếu user là Admin
    const isAdmin = await permissionService.isAdmin(userId);
    if (isAdmin) {
      return menus; // Admin xem tất cả menu
    }

    const filteredMenus = [];

    for (const menu of menus) {
      let hasPermission = true;

      // Kiểm tra quyền dựa trên table_id
      if (menu.table_id) {
        // Lấy thông tin table
        const adminService = require('../services/adminService');
        const table = await adminService.getAdminTableById(menu.table_id);
        
        if (table) {
          // Kiểm tra quyền read cho table này
          hasPermission = await permissionService.checkUserPermission(
            userId, null, table.name, 'read'
          );
        }
      } else {
        // Kiểm tra quyền dựa trên URL pattern
        if (menu.url) {
          if (menu.url.includes('/admin/tables')) {
            hasPermission = await permissionService.checkUserPermission(userId, 'read_admintable');
          } else if (menu.url.includes('/admin/menus')) {
            hasPermission = await permissionService.checkUserPermission(userId, null, 'admin_menus', 'browse');
          } else if (menu.url.includes('/admin/permissions')) {
            hasPermission = await permissionService.checkUserPermission(userId, 'read_permissions');
          } else if (menu.url.includes('/admin/roles')) {
            hasPermission = await permissionService.checkUserPermission(userId, 'read_roles');
          }
          // Các route khác như dashboard, home luôn được hiển thị
        }
      }

      if (hasPermission) {
        // Clone menu và filter children
        const filteredMenu = { ...menu };
        
        if (menu.children && menu.children.length > 0) {
          filteredMenu.children = await filterMenusByPermissions(menu.children, userId, permissionService);
          
          // Chỉ hiển thị parent menu nếu có ít nhất 1 child được phép xem
          if (filteredMenu.children.length > 0 || !menu.table_id) {
            filteredMenus.push(filteredMenu);
          }
        } else {
          filteredMenus.push(filteredMenu);
        }
      }
    }

    return filteredMenus;
  } catch (error) {
    console.error('Error filtering menus by permissions:', error);
    return menus; // Fallback: trả về tất cả menu nếu có lỗi
  }
}

module.exports = menuController;
